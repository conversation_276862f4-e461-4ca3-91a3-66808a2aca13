import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { Colors } from './Colors';

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: Colors.light.primary,
    primaryContainer: Colors.light.primaryLight,
    secondary: Colors.light.primaryDark,
    secondaryContainer: Colors.light.primaryLight,
    surface: Colors.light.surface,
    surfaceVariant: Colors.light.surfaceVariant,
    background: Colors.light.background,
    onPrimary: Colors.light.text,
    onPrimaryContainer: Colors.light.text,
    onSecondary: '#FFFFFF',
    onSecondaryContainer: Colors.light.text,
    onSurface: Colors.light.text,
    onSurfaceVariant: Colors.light.text,
    onBackground: Colors.light.text,
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: Colors.dark.primary,
    primaryContainer: Colors.dark.primaryLight,
    secondary: Colors.dark.primaryDark,
    secondaryContainer: Colors.dark.primaryLight,
    surface: Colors.dark.surface,
    surfaceVariant: Colors.dark.surfaceVariant,
    background: Colors.dark.background,
    onPrimary: Colors.dark.text,
    onPrimaryContainer: Colors.dark.text,
    onSecondary: '#000000',
    onSecondaryContainer: Colors.dark.text,
    onSurface: Colors.dark.text,
    onSurfaceVariant: Colors.dark.text,
    onBackground: Colors.dark.text,
  },
};
