/**
 * WCAG-compliant color palette with primary color #FEA405
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 */

// Primary brand colors
const primaryColor = "#FEA405"; // Main amber color
const primaryDark = "#B66B00"; // Darker variant for text on light backgrounds (7.16:1 contrast)
const primaryLight = "#FFD477"; // Lighter accent for dark backgrounds
const textPrimary = "#2B2B2B"; // High contrast text (10.97:1 with primary)
const backgroundLight = "#F7F7F7"; // Light neutral background

const tintColorLight = primaryColor;
const tintColorDark = primaryLight;

export const Colors = {
  light: {
    text: textPrimary,
    background: backgroundLight,
    tint: tintColorLight,
    icon: "#687076",
    tabIconDefault: "#687076",
    tabIconSelected: tintColorLight,
    // Brand colors
    primary: primaryColor,
    primaryDark: primaryDark,
    primaryLight: primaryLight,
    surface: "#FFFFFF",
    surfaceVariant: backgroundLight,
  },
  dark: {
    text: "#ECEDEE",
    background: "#151718",
    tint: tintColorDark,
    icon: "#9BA1A6",
    tabIconDefault: "#9BA1A6",
    tabIconSelected: tintColorDark,
    // Brand colors for dark mode
    primary: primaryLight,
    primaryDark: primaryColor,
    primaryLight: primaryColor,
    surface: "#1E1E1E",
    surfaceVariant: "#2A2A2A",
  },
};
