import { create } from 'zustand';
import { Product, ScannerMode, PantryState } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEY = 'pantry_products';

interface PantryActions {
  addProduct: (product: Omit<Product, 'id' | 'addedAt' | 'updatedAt'>) => Promise<void>;
  removeProduct: (barcode: string) => Promise<void>;
  updateProductQuantity: (barcode: string, quantity: number) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  setScannerMode: (mode: ScannerMode) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  loadProducts: () => Promise<void>;
  clearError: () => void;
}

type PantryStore = PantryState & PantryActions;

export const usePantryStore = create<PantryStore>((set, get) => ({
  products: [],
  scannerMode: 'add',
  isLoading: false,
  error: null,

  addProduct: async (productData) => {
    try {
      set({ isLoading: true, error: null });
      
      const existingProduct = get().products.find(p => p.barcode === productData.barcode);
      
      if (existingProduct) {
        // Update quantity if product exists
        await get().updateProductQuantity(productData.barcode, existingProduct.quantity + productData.quantity);
      } else {
        // Add new product
        const newProduct: Product = {
          ...productData,
          id: Date.now().toString(),
          addedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        const updatedProducts = [...get().products, newProduct];
        set({ products: updatedProducts });
        
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedProducts));
      }
    } catch (error) {
      set({ error: 'Failed to add product' });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  removeProduct: async (barcode) => {
    try {
      set({ isLoading: true, error: null });
      
      const existingProduct = get().products.find(p => p.barcode === barcode);
      
      if (existingProduct) {
        if (existingProduct.quantity > 1) {
          // Decrease quantity
          await get().updateProductQuantity(barcode, existingProduct.quantity - 1);
        } else {
          // Remove product completely
          const updatedProducts = get().products.filter(p => p.barcode !== barcode);
          set({ products: updatedProducts });
          
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedProducts));
        }
      } else {
        throw new Error('Product not found in pantry');
      }
    } catch (error) {
      set({ error: 'Failed to remove product' });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  updateProductQuantity: async (barcode, quantity) => {
    try {
      set({ isLoading: true, error: null });
      
      const updatedProducts = get().products.map(product =>
        product.barcode === barcode
          ? { ...product, quantity, updatedAt: new Date().toISOString() }
          : product
      );
      
      set({ products: updatedProducts });
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedProducts));
    } catch (error) {
      set({ error: 'Failed to update product quantity' });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  deleteProduct: async (id) => {
    try {
      set({ isLoading: true, error: null });
      
      const updatedProducts = get().products.filter(p => p.id !== id);
      set({ products: updatedProducts });
      
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedProducts));
    } catch (error) {
      set({ error: 'Failed to delete product' });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  setScannerMode: (mode) => {
    set({ scannerMode: mode });
  },

  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  setError: (error) => {
    set({ error });
  },

  loadProducts: async () => {
    try {
      set({ isLoading: true, error: null });
      
      const storedProducts = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedProducts) {
        const products = JSON.parse(storedProducts);
        set({ products });
      }
    } catch (error) {
      set({ error: 'Failed to load products' });
    } finally {
      set({ isLoading: false });
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
