import { ErrorClassFields, ErrorTypes } from "./types";
import { getErrorMessage, getErrorStatus } from "./getErrorStateInfo";

export abstract class ErrorBase implements ErrorClassFields {
  type: ErrorTypes;
  code: number;
  message?: string;

  constructor(type: ErrorTypes, error?: unknown) {
    this.code = getErrorStatus(error);
    this.message = getErrorMessage(error);
    this.type = type;
  }
}
