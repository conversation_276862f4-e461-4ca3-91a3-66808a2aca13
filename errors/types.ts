export type ErrorClassFields = {
  type: ErrorTypes;
  code: number;
  message?: string;
};

export enum ErrorTypes {
  UNKNOWN = "UNKNOWN",
  BARCODE_SCANNER = "BARCODE_SCANNER",
  CAMERA_PERMISSION = "CAMERA_PERMISSION",
  PRODUCT_FETCH = "PRODUCT_FETCH",
  PRODUCT_SAVE = "PRODUCT_SAVE",
  PRODUCT_DELETE = "PRODUCT_DELETE",
  PRODUCT_UPDATE = "PRODUCT_UPDATE",
  STORAGE_ERROR = "STORAGE_ERROR",
  API_CONNECTION = "API_CONNECTION",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  MANUAL_PRODUCT_ENTRY = "MANUAL_PRODUCT_ENTRY",
}
