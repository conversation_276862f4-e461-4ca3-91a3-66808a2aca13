import { ErrorBase } from "./ErrorBase";
import { ErrorTypes } from "./types";

export class ErrorBarcodeScanner extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.BARCODE_SCANNER, error);
  }
}

export class <PERSON>rrorCameraPermission extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.CAMERA_PERMISSION, error);
  }
}

export class ErrorProductFetch extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PRODUCT_FETCH, error);
  }
}

export class ErrorProductSave extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PRODUCT_SAVE, error);
  }
}

export class ErrorProductDelete extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PRODUCT_DELETE, error);
  }
}

export class ErrorProductUpdate extends E<PERSON>rBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PRODUCT_UPDATE, error);
  }
}

export class ErrorStorage extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.STORAGE_ERROR, error);
  }
}

export class ErrorApiConnection extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.API_CONNECTION, error);
  }
}

export class ErrorValidation extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.VALIDATION_ERROR, error);
  }
}

export class ErrorManualProductEntry extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.MANUAL_PRODUCT_ENTRY, error);
  }
}
