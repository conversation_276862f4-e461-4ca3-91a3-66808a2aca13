export interface Product {
  id: string;
  barcode: string;
  name: string;
  description?: string;
  brand?: string;
  imageUrl?: string;
  quantity: number;
  addedAt: string;
  updatedAt: string;
  isManualEntry: boolean;
}

export interface ProductFromAPI {
  product_name?: string;
  brands?: string;
  image_url?: string;
  generic_name?: string;
  categories?: string;
}

export interface OpenFoodFactsResponse {
  status: number;
  status_verbose: string;
  product?: ProductFromAPI;
}

export type ScannerMode = 'add' | 'remove';

export interface PantryState {
  products: Product[];
  scannerMode: ScannerMode;
  isLoading: boolean;
  error: string | null;
}
