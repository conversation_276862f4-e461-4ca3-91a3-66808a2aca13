# BarcodePantry 📱

A React Native mobile application for managing your pantry inventory through barcode scanning. Built with Expo, React Native Paper, and TypeScript.

## 🎯 Features

- **Barcode Scanning**: Scan product barcodes to add/remove items from your pantry
- **Dual Scanner Modes**: Switch between Add and Remove modes
- **Product Database Integration**: Automatic product information fetching from Open Food Facts API
- **Manual Product Entry**: Add products manually when barcode scanning fails
- **Local Storage**: All data stored locally using AsyncStorage
- **Multilingual Support**: Built-in internationalization (currently English)
- **Modern UI**: Clean interface using React Native Paper components

## 🛠️ Tech Stack

- **Framework**: React Native + Expo
- **Language**: TypeScript
- **State Management**: Zustand
- **UI Library**: React Native Paper
- **Navigation**: Expo Router
- **Forms**: React Hook Form + Zod validation
- **API**: Open Food Facts API
- **Storage**: AsyncStorage
- **Internationalization**: i18next
- **Camera**: Expo Camera + Barcode Scanner

## 📱 Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd BarcodePantry
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start the development server**

   ```bash
   npx expo start
   ```

4. **Run on device/simulator**
   - Scan QR code with Expo Go app (iOS/Android)
   - Press `i` for iOS simulator
   - Press `a` for Android emulator

## 🏗️ Project Structure

```
├── api/                 # API services (Open Food Facts)
├── app/                 # App screens (Expo Router)
├── assets/              # Static assets
├── components/          # Reusable components
├── contexts/            # React contexts
├── errors/              # Error handling classes
├── hooks/               # Custom hooks
├── locales/             # Internationalization files
├── stores/              # Zustand stores
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
└── validations/         # Zod validation schemas
```

## 🚀 Building for Production

### Using EAS Build

1. **Install EAS CLI**

   ```bash
   npm install -g @expo/eas-cli
   ```

2. **Login to Expo**

   ```bash
   eas login
   ```

3. **Configure your project**

   ```bash
   eas build:configure
   ```

4. **Build for iOS/Android**

   ```bash
   # Development build
   eas build --profile development --platform all

   # Production build
   eas build --profile production --platform all
   ```

## 📋 Usage

1. **Scanner Tab**:

   - Point camera at product barcode
   - Switch between Add/Remove modes
   - Products automatically added to/removed from pantry

2. **Pantry Tab**:

   - View all products in your pantry
   - See quantities and product details
   - Delete products manually

3. **Manual Tab**:
   - Add products manually when barcode scanning fails
   - Fill in product name, barcode, brand, description, and quantity

## 🔧 Configuration

### Environment Variables

No environment variables required - the app uses the free Open Food Facts API.

### Permissions

- **Camera**: Required for barcode scanning
- **Storage**: Required for local data persistence

## 🧪 Testing

```bash
# Type checking
npx tsc --noEmit

# Linting
npm run lint
```

## 📝 MVP Features Checklist

- [x] Barcode scanning with add/remove modes
- [x] Open Food Facts API integration
- [x] Local storage with AsyncStorage
- [x] Manual product entry
- [x] Modern UI with React Native Paper
- [x] Error handling and user feedback
- [x] TypeScript support
- [x] Internationalization ready

## 🔮 Future Enhancements

- [ ] Cloud synchronization
- [ ] Family sharing
- [ ] Expiration date tracking
- [ ] Shopping list generation
- [ ] Recipe suggestions
- [ ] Barcode generation for custom products
- [ ] Analytics and insights

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Open Food Facts](https://world.openfoodfacts.org/) for the free product database API
- [Expo](https://expo.dev/) for the amazing development platform
- [React Native Paper](https://reactnativepaper.com/) for the beautiful UI components
