{"common": {"add": "Add", "remove": "Remove", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "retry": "Retry", "close": "Close"}, "scanner": {"title": "Barcode Scanner", "addMode": "Add Mode", "removeMode": "Remove Mode", "switchToAdd": "Switch to Add Mode", "switchToRemove": "Switch to Remove Mode", "scanPrompt": "Point camera at barcode", "cameraPermissionDenied": "Camera permission denied", "cameraPermissionRequired": "Camera permission is required to scan barcodes", "scanSuccess": "Product scanned successfully", "scanError": "Failed to scan barcode", "productNotFound": "Product not found in database", "productAdded": "Product added to pantry", "productRemoved": "Product removed from pantry", "productUpdated": "Product quantity updated"}, "pantry": {"title": "My Pantry", "empty": "Your pantry is empty", "emptyDescription": "Start by scanning some products", "quantity": "Quantity: {{count}}", "addedOn": "Added on {{date}}", "manualEntry": "Manual Entry", "fromAPI": "From Database"}, "manualEntry": {"title": "Add Product Manually", "productName": "Product Name", "productNamePlaceholder": "Enter product name", "description": "Description", "descriptionPlaceholder": "Enter description (optional)", "brand": "Brand", "brandPlaceholder": "Enter brand (optional)", "barcode": "Barcode", "barcodePlaceholder": "Enter barcode", "quantity": "Quantity", "quantityPlaceholder": "Enter quantity", "addProduct": "Add Product", "productAdded": "Product added successfully", "validationError": "Please fill in all required fields"}, "errors": {"unknown": "An unknown error occurred", "barcodeScanner": "Barcode scanner error", "cameraPermission": "Camera permission error", "productFetch": "Failed to fetch product information", "productSave": "Failed to save product", "productDelete": "Failed to delete product", "productUpdate": "Failed to update product", "storageError": "Storage error", "apiConnection": "API connection error", "validationError": "Validation error", "manualProductEntry": "Manual product entry error"}, "navigation": {"scanner": "Scanner", "pantry": "Pantry", "manual": "Manual"}}