import { ManualProductEntry } from "@/components";
import React from "react";
import { useTranslation } from "react-i18next";
import { StyleSheet } from "react-native";
import { Appbar } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";

export default function ManualScreen() {
  const { t } = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={t("manualEntry.title")} />
      </Appbar.Header>
      <ManualProductEntry />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F7F7", // Using new background color
  },
});
