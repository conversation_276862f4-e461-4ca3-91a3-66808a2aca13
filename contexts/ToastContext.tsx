import React, { createContext, ReactNode, useContext, useState } from "react";

interface ToastContextType {
  showToast: (toast: React.ReactElement) => void;
  hideToast: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [currentToast, setCurrentToast] = useState<React.ReactElement | null>(
    null
  );
  const [visible, setVisible] = useState(false);

  const showToast = (toast: React.ReactElement) => {
    setCurrentToast(toast);
    setVisible(true);
  };

  const hideToast = () => {
    setVisible(false);
    setTimeout(() => setCurrentToast(null), 300);
  };

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      {currentToast &&
        React.cloneElement(currentToast, {
          visible,
          onDismiss: hideToast,
        } as any)}
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};
