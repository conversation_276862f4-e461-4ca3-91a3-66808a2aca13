import axios from 'axios';
import { OpenFoodFactsResponse, ProductFromAPI } from '@/types';

const OPEN_FOOD_FACTS_BASE_URL = 'https://world.openfoodfacts.org/api/v0/product';

export const openFoodFactsApi = axios.create({
  baseURL: OPEN_FOOD_FACTS_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'BarcodePantry/1.0.0',
  },
});

export const fetchProductByBarcode = async (barcode: string): Promise<ProductFromAPI | null> => {
  try {
    const response = await openFoodFactsApi.get<OpenFoodFactsResponse>(`/${barcode}.json`);
    
    if (response.data.status === 1 && response.data.product) {
      return response.data.product;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching product from Open Food Facts:', error);
    throw error;
  }
};
