import { z } from 'zod';

export const manualProductSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(100, 'Product name is too long'),
  description: z.string().max(500, 'Description is too long').optional(),
  brand: z.string().max(100, 'Brand name is too long').optional(),
  barcode: z.string().min(1, 'Barcode is required').regex(/^\d+$/, 'Barcode must contain only numbers'),
  quantity: z.number().min(1, 'Quantity must be at least 1').max(9999, 'Quantity is too large'),
});

export type ManualProductFormData = z.infer<typeof manualProductSchema>;
