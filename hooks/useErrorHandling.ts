import { ToastError } from "@/components/Toast";
import { useToast } from "@/contexts";
import { ErrorClassFields } from "@/errors";
import { ToastProps } from "@/types";
import { buildErrorMessage } from "@/utils";
import React from "react";

export const useErrorHandling = () => {
  const { showToast } = useToast();

  const withErrorHandling = async <T>(
    operation: () => Promise<T>,
    extraOnError?: () => void
  ): Promise<T | undefined> => {
    try {
      return await operation();
    } catch (error) {
      const errorMessage: ToastProps = buildErrorMessage(
        error as ErrorClassFields
      );
      showToast(React.createElement(ToastError, errorMessage));

      if (extraOnError) {
        extraOnError();
      }
    }
  };

  return {
    withErrorHandling,
  };
};
