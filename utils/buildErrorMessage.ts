import { ErrorClassFields, ErrorTypes } from '@/errors';
import { ToastProps } from '@/types';

export const buildErrorMessage = (error: ErrorClassFields): ToastProps => {
  const getErrorTitle = (type: ErrorTypes): string => {
    switch (type) {
      case ErrorTypes.BARCODE_SCANNER:
        return 'Scanner Error';
      case ErrorTypes.CAMERA_PERMISSION:
        return 'Camera Permission';
      case ErrorTypes.PRODUCT_FETCH:
        return 'Product Fetch Error';
      case ErrorTypes.PRODUCT_SAVE:
        return 'Save Error';
      case ErrorTypes.PRODUCT_DELETE:
        return 'Delete Error';
      case ErrorTypes.PRODUCT_UPDATE:
        return 'Update Error';
      case ErrorTypes.STORAGE_ERROR:
        return 'Storage Error';
      case ErrorTypes.API_CONNECTION:
        return 'Connection Error';
      case ErrorTypes.VALIDATION_ERROR:
        return 'Validation Error';
      case ErrorTypes.MANUAL_PRODUCT_ENTRY:
        return 'Entry Error';
      default:
        return 'Error';
    }
  };

  return {
    title: getErrorTitle(error.type),
    message: error.message || 'An unknown error occurred',
    type: 'error',
    duration: 4000,
  };
};
