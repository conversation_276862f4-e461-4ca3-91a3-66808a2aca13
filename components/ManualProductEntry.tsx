import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { TextInput, Button, Surface, Text } from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { manualProductSchema, ManualProductFormData } from '@/validations';
import { usePantryStore } from '@/stores';
import { useErrorHandling } from '@/hooks';
import { ErrorManualProductEntry } from '@/errors';
import { ToastSuccess } from './Toast';
import { useToast } from '@/contexts';

interface ManualProductEntryProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const ManualProductEntry: React.FC<ManualProductEntryProps> = ({
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation();
  const { showToast } = useToast();
  const { withErrorHandling } = useErrorHandling();
  const { addProduct, isLoading } = usePantryStore();

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ManualProductFormData>({
    resolver: zodResolver(manualProductSchema),
    defaultValues: {
      name: '',
      description: '',
      brand: '',
      barcode: '',
      quantity: 1,
    },
  });

  const onSubmit = async (data: ManualProductFormData) => {
    await withErrorHandling(async () => {
      try {
        await addProduct({
          ...data,
          isManualEntry: true,
        });

        showToast(
          <ToastSuccess
            title={t('manualEntry.productAdded')}
            message={`${data.name} has been added to your pantry`}
            type="success"
          />
        );

        reset();
        onSuccess?.();
      } catch (error) {
        throw new ErrorManualProductEntry(error);
      }
    });
  };

  return (
    <Surface style={styles.container} elevation={2}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Text variant="headlineSmall" style={styles.title}>
          {t('manualEntry.title')}
        </Text>

        <View style={styles.form}>
          <Controller
            control={control}
            name="name"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label={t('manualEntry.productName')}
                placeholder={t('manualEntry.productNamePlaceholder')}
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.name}
                style={styles.input}
                mode="outlined"
              />
            )}
          />
          {errors.name && (
            <Text style={styles.errorText}>{errors.name.message}</Text>
          )}

          <Controller
            control={control}
            name="barcode"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label={t('manualEntry.barcode')}
                placeholder={t('manualEntry.barcodePlaceholder')}
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.barcode}
                style={styles.input}
                mode="outlined"
                keyboardType="numeric"
              />
            )}
          />
          {errors.barcode && (
            <Text style={styles.errorText}>{errors.barcode.message}</Text>
          )}

          <Controller
            control={control}
            name="brand"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label={t('manualEntry.brand')}
                placeholder={t('manualEntry.brandPlaceholder')}
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.brand}
                style={styles.input}
                mode="outlined"
              />
            )}
          />
          {errors.brand && (
            <Text style={styles.errorText}>{errors.brand.message}</Text>
          )}

          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label={t('manualEntry.description')}
                placeholder={t('manualEntry.descriptionPlaceholder')}
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.description}
                style={styles.input}
                mode="outlined"
                multiline
                numberOfLines={3}
              />
            )}
          />
          {errors.description && (
            <Text style={styles.errorText}>{errors.description.message}</Text>
          )}

          <Controller
            control={control}
            name="quantity"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label={t('manualEntry.quantity')}
                placeholder={t('manualEntry.quantityPlaceholder')}
                value={value.toString()}
                onBlur={onBlur}
                onChangeText={(text) => onChange(parseInt(text) || 1)}
                error={!!errors.quantity}
                style={styles.input}
                mode="outlined"
                keyboardType="numeric"
              />
            )}
          />
          {errors.quantity && (
            <Text style={styles.errorText}>{errors.quantity.message}</Text>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSubmit(onSubmit)}
            loading={isLoading}
            disabled={isLoading}
            style={styles.submitButton}
          >
            {t('manualEntry.addProduct')}
          </Button>
          
          {onCancel && (
            <Button
              mode="outlined"
              onPress={onCancel}
              disabled={isLoading}
              style={styles.cancelButton}
            >
              {t('common.cancel')}
            </Button>
          )}
        </View>
      </ScrollView>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContainer: {
    padding: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold',
  },
  form: {
    marginBottom: 24,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginBottom: 16,
    marginLeft: 12,
  },
  buttonContainer: {
    gap: 12,
  },
  submitButton: {
    paddingVertical: 8,
  },
  cancelButton: {
    paddingVertical: 8,
  },
});
