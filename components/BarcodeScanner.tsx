import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { Button, Surface, IconButton } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePantryStore } from '@/stores';
import { fetchProductByBarcode } from '@/api';
import { useErrorHandling } from '@/hooks';
import { ErrorBarcodeScanner, ErrorCameraPermission, ErrorProductFetch } from '@/errors';
import { ToastSuccess } from './Toast';
import { useToast } from '@/contexts';

interface BarcodeScannerProps {
  onClose?: () => void;
}

export const BarcodeScanner: React.FC<BarcodeScannerProps> = ({ onClose }) => {
  const { t } = useTranslation();
  const { showToast } = useToast();
  const { withErrorHandling } = useErrorHandling();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [facing, setFacing] = useState<CameraType>('back');
  
  const { 
    scannerMode, 
    setScannerMode, 
    addProduct, 
    removeProduct, 
    isLoading 
  } = usePantryStore();

  useEffect(() => {
    if (!permission?.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  const handleBarCodeScanned = async ({ type, data }: { type: string; data: string }) => {
    if (scanned || isLoading) return;
    
    setScanned(true);
    
    await withErrorHandling(async () => {
      if (scannerMode === 'add') {
        await handleAddProduct(data);
      } else {
        await handleRemoveProduct(data);
      }
    });

    // Reset scanner after 2 seconds
    setTimeout(() => setScanned(false), 2000);
  };

  const handleAddProduct = async (barcode: string) => {
    try {
      // Try to fetch product from API
      const apiProduct = await fetchProductByBarcode(barcode);
      
      const productData = {
        barcode,
        name: apiProduct?.product_name || `Product ${barcode}`,
        description: apiProduct?.generic_name || apiProduct?.categories || undefined,
        brand: apiProduct?.brands || undefined,
        imageUrl: apiProduct?.image_url || undefined,
        quantity: 1,
        isManualEntry: !apiProduct,
      };

      await addProduct(productData);
      
      showToast(
        <ToastSuccess
          title={t('scanner.scanSuccess')}
          message={t('scanner.productAdded')}
          type="success"
        />
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('fetch')) {
        throw new ErrorProductFetch(error);
      }
      throw new ErrorBarcodeScanner(error);
    }
  };

  const handleRemoveProduct = async (barcode: string) => {
    try {
      await removeProduct(barcode);
      
      showToast(
        <ToastSuccess
          title={t('scanner.scanSuccess')}
          message={t('scanner.productRemoved')}
          type="success"
        />
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('not found')) {
        showToast(
          <ToastSuccess
            title={t('scanner.productNotFound')}
            message="Product not found in your pantry"
            type="warning"
          />
        );
      } else {
        throw new ErrorBarcodeScanner(error);
      }
    }
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const toggleScannerMode = () => {
    setScannerMode(scannerMode === 'add' ? 'remove' : 'add');
  };

  if (!permission) {
    return <View style={styles.container}><Text>Requesting camera permission...</Text></View>;
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>{t('scanner.cameraPermissionRequired')}</Text>
        <Button mode="contained" onPress={requestPermission}>
          Grant Permission
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>{t('scanner.title')}</Text>
          {onClose && (
            <IconButton icon="close" onPress={onClose} />
          )}
        </View>
        <View style={styles.modeContainer}>
          <Button
            mode={scannerMode === 'add' ? 'contained' : 'outlined'}
            onPress={() => setScannerMode('add')}
            style={styles.modeButton}
          >
            {t('scanner.addMode')}
          </Button>
          <Button
            mode={scannerMode === 'remove' ? 'contained' : 'outlined'}
            onPress={() => setScannerMode('remove')}
            style={styles.modeButton}
          >
            {t('scanner.removeMode')}
          </Button>
        </View>
      </Surface>

      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing={facing}
          onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ['ean13', 'ean8', 'upc_a', 'upc_e', 'code128', 'code39'],
          }}
        >
          <View style={styles.overlay}>
            <View style={styles.scanArea} />
            <Text style={styles.scanPrompt}>
              {scanned ? 'Processing...' : t('scanner.scanPrompt')}
            </Text>
          </View>
        </CameraView>
      </View>

      <Surface style={styles.controls} elevation={2}>
        <IconButton
          icon="camera-flip"
          mode="contained"
          onPress={toggleCameraFacing}
        />
        <Button
          mode="outlined"
          onPress={toggleScannerMode}
          style={styles.toggleButton}
        >
          {scannerMode === 'add' ? t('scanner.switchToRemove') : t('scanner.switchToAdd')}
        </Button>
      </Surface>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  scanPrompt: {
    color: 'white',
    fontSize: 16,
    marginTop: 20,
    textAlign: 'center',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  toggleButton: {
    flex: 1,
    marginLeft: 16,
  },
  message: {
    textAlign: 'center',
    paddingBottom: 10,
  },
});
