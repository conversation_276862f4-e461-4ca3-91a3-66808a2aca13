import React from 'react';
import { View, FlatList, StyleSheet, Image } from 'react-native';
import { Card, Text, IconButton, Chip, Surface } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePantryStore } from '@/stores';
import { Product } from '@/types';
import { formatDate } from '@/utils';
import { useErrorHandling } from '@/hooks';
import { ErrorProductDelete } from '@/errors';

interface PantryListProps {
  onProductPress?: (product: Product) => void;
}

export const PantryList: React.FC<PantryListProps> = ({ onProductPress }) => {
  const { t } = useTranslation();
  const { withErrorHandling } = useErrorHandling();
  const { products, deleteProduct, isLoading } = usePantryStore();

  const handleDeleteProduct = async (productId: string) => {
    await withErrorHandling(async () => {
      await deleteProduct(productId);
    });
  };

  const renderProduct = ({ item }: { item: Product }) => (
    <Card style={styles.card} onPress={() => onProductPress?.(item)}>
      <Card.Content>
        <View style={styles.productHeader}>
          <View style={styles.productInfo}>
            {item.imageUrl && (
              <Image source={{ uri: item.imageUrl }} style={styles.productImage} />
            )}
            <View style={styles.productDetails}>
              <Text variant="titleMedium" style={styles.productName}>
                {item.name}
              </Text>
              {item.brand && (
                <Text variant="bodySmall" style={styles.brand}>
                  {item.brand}
                </Text>
              )}
              {item.description && (
                <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
                  {item.description}
                </Text>
              )}
            </View>
          </View>
          <IconButton
            icon="delete"
            onPress={() => handleDeleteProduct(item.id)}
            disabled={isLoading}
          />
        </View>
        
        <View style={styles.productFooter}>
          <View style={styles.quantityContainer}>
            <Text variant="bodyMedium" style={styles.quantity}>
              {t('pantry.quantity', { count: item.quantity })}
            </Text>
            <Chip
              mode="outlined"
              compact
              style={styles.typeChip}
            >
              {item.isManualEntry ? t('pantry.manualEntry') : t('pantry.fromAPI')}
            </Chip>
          </View>
          <Text variant="bodySmall" style={styles.date}>
            {t('pantry.addedOn', { date: formatDate(item.addedAt) })}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <Surface style={styles.emptyContainer} elevation={1}>
      <Text variant="headlineSmall" style={styles.emptyTitle}>
        {t('pantry.empty')}
      </Text>
      <Text variant="bodyMedium" style={styles.emptyDescription}>
        {t('pantry.emptyDescription')}
      </Text>
    </Surface>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={products}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  card: {
    marginBottom: 12,
    backgroundColor: 'white',
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  productInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  brand: {
    color: '#666',
    marginBottom: 4,
  },
  description: {
    color: '#888',
    lineHeight: 18,
  },
  productFooter: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 12,
  },
  quantityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  quantity: {
    fontWeight: '600',
    color: '#2196F3',
  },
  typeChip: {
    height: 24,
  },
  date: {
    color: '#999',
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    margin: 16,
    borderRadius: 12,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#666',
  },
  emptyDescription: {
    textAlign: 'center',
    color: '#999',
  },
});
