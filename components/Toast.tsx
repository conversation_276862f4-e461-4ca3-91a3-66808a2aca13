import { ToastProps } from "@/types";
import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Snackbar } from "react-native-paper";

interface ToastComponentProps extends ToastProps {
  visible: boolean;
  onDismiss: () => void;
}

export const Toast: React.FC<ToastComponentProps> = ({
  title,
  message,
  type,
  duration = 4000,
  visible,
  onDismiss,
}) => {
  const getBackgroundColor = () => {
    switch (type) {
      case "success":
        return "#4CAF50";
      case "error":
        return "#F44336";
      case "warning":
        return "#FF9800";
      case "info":
        return "#2196F3";
      default:
        return "#323232";
    }
  };

  return (
    <Snackbar
      visible={visible}
      onDismiss={onDismiss}
      duration={duration}
      style={[styles.snackbar, { backgroundColor: getBackgroundColor() }]}
      action={{
        label: "Close",
        onPress: onDismiss,
        textColor: "white",
      }}
    >
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>
      </View>
    </Snackbar>
  );
};

export const ToastError: React.FC<ToastProps> = (props) => (
  <Toast {...props} type="error" visible={false} onDismiss={() => {}} />
);

export const ToastSuccess: React.FC<ToastProps> = (props) => (
  <Toast {...props} type="success" visible={false} onDismiss={() => {}} />
);

export const ToastWarning: React.FC<ToastProps> = (props) => (
  <Toast {...props} type="warning" visible={false} onDismiss={() => {}} />
);

export const ToastInfo: React.FC<ToastProps> = (props) => (
  <Toast {...props} type="info" visible={false} onDismiss={() => {}} />
);

const styles = StyleSheet.create({
  snackbar: {
    marginBottom: 50,
  },
  content: {
    flexDirection: "column",
  },
  title: {
    color: "white",
    fontWeight: "bold",
    fontSize: 16,
  },
  message: {
    color: "white",
    fontSize: 14,
    marginTop: 4,
  },
});
